Running 4 tests for test/recon/CryticToFoundry.sol:CryticToFoundry


╭─────────────────────── Initial Invariant Target Functions ───────────────────────╮
│ CryticToFoundry.sol:CryticToFoundry @ 0x7fa9385be102ac3eac297483dd6233d62b3e1496 │
│ ├── add_new_asset(uint8)                                                         │
│ ├── asset_approve(address,uint128)                                               │
│ ├── asset_mint(address,uint128)                                                  │
│ ├── counter_increment_asAdmin()                                                  │
│ ├── doomsday_increment_never_reverts()                                           │
│ ├── increaseAmt(uint256)                                                         │
│ ├── setIsManager(address,bool)                                                   │
│ ├── setTheManager(address)                                                       │
│ ├── switchActor(uint256)                                                         │
│ └── switch_asset(uint256)                                                        │
╰──────────────────────────────────────────────────────────────────────────────────╯


Counterexample: ∅
Counterexample: 
    halmos_block_timestamp_depth1_24e077e = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_11c31dd_03 = 0x00
    p_decimals_uint8_b2b4d3d_04 = 0x00
Sequence:
    CALL CryticToFoundry::add_new_asset(p_decimals_uint8_b2b4d3d_04) (value: halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_11c31dd_03) (caller: halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_3631ef6_02)
        CREATE 0xaaaa0004::<7875 bytes of initcode>
            SLOAD  @0 → 0x0000000000000000000000000000000000000000000000000000000000000000
            SSTORE @0 ← 0x5465737420546f6b656e00000000000000000000000000000000000000000014
            SLOAD  @1 → 0x0000000000000000000000000000000000000000000000000000000000000000
            SSTORE @1 ← 0x5453540000000000000000000000000000000000000000000000000000000006
            SLOAD  @0 → 0x5465737420546f6b656e00000000000000000000000000000000000000000014
        ↩ RETURN <5873 bytes of code>
        SLOAD  @0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 → 0x00
        SLOAD  @0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 → 0x00
        SLOAD  @4 → 0x0000000000000000000000000000000000000000000000000000000000000001
        SSTORE @4 ← 0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @4 → 0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @0x8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19c → 0x00
        SSTORE @0x8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19c ← 0x00000000000000000000000000000000000000000000000000000000aaaa0004
        SLOAD  @4 → 0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 → 0x00
        SSTORE @0xef1ff036d721178726bcbe32627361c13952af5172157c2d22b8d916704d83a4 ← 0x0000000000000000000000000000000000000000000000000000000000000002
        SLOAD  @3 → 0x00000000000000000000000000000000000000000000000000000000aaaa0002
        SSTORE @3 ← 0x00000000000000000000000000000000000000000000000000000000aaaa0004
    ↩ RETURN 0x00000000000000000000000000000000000000000000000000000000aaaa0004

Counterexample: 
    halmos_block_timestamp_depth1_35fd8e1 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_f9163e1_07 = 0x00
    p_amt_uint128_db4946c_09 = 0x00
    p_to_address_d838b72_08 = 0x00
Sequence:
    CALL CryticToFoundry::asset_approve(Concat(p_to_address_d838b72_08, p_amt_uint128_db4946c_09)) (value: halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_f9163e1_07) (caller: halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6908987_06)
        SLOAD  @6 → 0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @12 → 0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @12 ← 0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @0 → 0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496
        CALL hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) (caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @3 → 0x00000000000000000000000000000000000000000000000000000000aaaa0002
        SLOAD  @3 → 0x00000000000000000000000000000000000000000000000000000000aaaa0002
        CALL 0xaaaa0002::approve(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_d838b72_08), 0x00000000000000000000000000000000, Extract(0x7f, 0x00, p_amt_uint128_db4946c_09))) (caller: CryticToFoundry)
            SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_d838b72_08), 0x9a37937c0ede8c307bf0496ec60e131422d44420befc187978dc5326d56d38a4)) → Select(storage_0x00000000000000000000000000000000aaaa0002_4_4_1024_3cb9c6f_09, Concat(Concat(Concat(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496, 0x0000000000000000000000000000000000000000000000000000000000000000), Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_d838b72_08))), 0x0000000000000000000000000000000000000000000000000000000000000000))
            SSTORE @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_d838b72_08), 0x9a37937c0ede8c307bf0496ec60e131422d44420befc187978dc5326d56d38a4)) ← Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, p_amt_uint128_db4946c_09))
            LOG3(topic0=0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925, topic1=0x7fa9385be102ac3eac297483dd6233d62b3e1496, topic2=Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_d838b72_08)), data=Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, p_amt_uint128_db4946c_09)))
        ↩ RETURN 0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @6 → 0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @13 → 0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @13 ← 0x0000000000000000000000000000000000000000000000000000000000000001
    ↩ RETURN 0x

Counterexample: 
    halmos_block_timestamp_depth1_19df0ce = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_65b875c_12 = 0x00
    p_amt_uint128_8d3dedb_14 = 0x00
    p_to_address_b2448c2_13 = 0x00
Sequence:
    CALL CryticToFoundry::asset_mint(Concat(p_to_address_b2448c2_13, p_amt_uint128_8d3dedb_14)) (value: halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_65b875c_12) (caller: halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_24b0ea7_11)
        SLOAD  @6 → 0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @12 → 0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @12 ← 0x0000000000000000000000000000000000000000000000000000000000000001
        CALL hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) (caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @3 → 0x00000000000000000000000000000000000000000000000000000000aaaa0002
        SLOAD  @3 → 0x00000000000000000000000000000000000000000000000000000000aaaa0002
        CALL 0xaaaa0002::mint(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_b2448c2_13), 0x00000000000000000000000000000000, Extract(0x7f, 0x00, p_amt_uint128_8d3dedb_14))) (caller: CryticToFoundry)
            SLOAD  @2 → 0x000000000000000000000000000000000000000001fffffffffffffffffffffe
            SLOAD  @2 → 0x000000000000000000000000000000000000000001fffffffffffffffffffffe
            SLOAD  @2 → 0x000000000000000000000000000000000000000000000000000000000000000
            SSTORE @2 ← +(0x000000000000000000000000000000000000000001fffffffffffffffffffffe, Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, p_amt_uint128_8d3dedb_14)))
            SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_b2448c2_13), 0x0000000000000000000000000000000000000000000000000000000000000003)) → Select(storage_0x00000000000000000000000000000000aaaa0002_3_2_512_4060a59_08, Concat(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_b2448c2_13)), 0x0000000000000000000000000000000000000000000000000000000000000000))
            SLOAD  @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_b2448c2_13), 0x0000000000000000000000000000000000000000000000000000000000000003)) → Select(storage_0x00000000000000000000000000000000aaaa0002_3_2_512_4060a59_08, Concat(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_b2448c2_13)), 0x0000000000000000000000000000000000000000000000000000000000000000))
            SSTORE @f_sha3_512(Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_b2448c2_13), 0x0000000000000000000000000000000000000000000000000000000000000003)) ← +(Select(storage_0x00000000000000000000000000000000aaaa0002_3_2_512_4060a59_08, Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_b2448c2_13), 0x0000000000000000000000000000000000000000000000000000000000000000)), Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, p_amt_uint128_8d3dedb_14)))
            LOG3(topic0=0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef, topic1=0x00, topic2=Concat(0x000000000000000000000000, Extract(0x9f, 0x00, p_to_address_b2448c2_13)), data=Concat(0x00000000000000000000000000000000, Extract(0x7f, 0x00, p_amt_uint128_8d3dedb_14)))
        ↩ RETURN 0x
        SLOAD  @6 → 0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @13 → 0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @13 ← 0x0000000000000000000000000000000000000000000000000000000000000001
    ↩ RETURN 0x
